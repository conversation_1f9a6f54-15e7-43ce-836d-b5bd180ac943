<template>
  <v-container class="pa-6" fluid>
    <!-- <PERSON> Header -->
    <v-row class="mb-6">
      <v-col>
        <div class="d-flex align-center justify-space-between">
          <div>
            <h1 class="text-h3 font-weight-bold mb-2">Projects</h1>
            <p class="text-subtitle-1 text-medium-emphasis">
              Organize and manage your project portfolio
            </p>
          </div>
          <div class="d-flex align-center ga-2">
            <v-btn
              :color="viewMode === 'cards' ? 'primary' : 'default'"
              :variant="viewMode === 'cards' ? 'elevated' : 'outlined'"
              icon="mdi-view-grid"
              @click="viewMode = 'cards'"
            />
            <v-btn
              :color="viewMode === 'list' ? 'primary' : 'default'"
              :variant="viewMode === 'list' ? 'elevated' : 'outlined'"
              icon="mdi-view-list"
              @click="viewMode = 'list'"
            />
            <v-btn
              color="primary"
              prepend-icon="mdi-plus"
              variant="elevated"
              @click="showCreateDialog = true"
            >
              New Project
            </v-btn>
          </div>
        </div>
      </v-col>
    </v-row>

    <!-- Projects Overview Stats -->
    <v-row class="mb-6">
      <v-col cols="12" lg="3" md="6">
        <v-card color="primary" variant="tonal">
          <v-card-text class="text-center">
            <v-icon class="mb-2" size="32">mdi-folder-multiple</v-icon>
            <div class="text-h4">{{ projectsStore.projectsCount }}</div>
            <div class="text-body-2">Total Projects</div>
          </v-card-text>
        </v-card>
      </v-col>
      <v-col cols="12" lg="3" md="6">
        <v-card color="success" variant="tonal">
          <v-card-text class="text-center">
            <v-icon class="mb-2" size="32">mdi-check-circle</v-icon>
            <div class="text-h4">{{ completedProjects }}</div>
            <div class="text-body-2">Completed Projects</div>
          </v-card-text>
        </v-card>
      </v-col>
      <v-col cols="12" lg="3" md="6">
        <v-card color="warning" variant="tonal">
          <v-card-text class="text-center">
            <v-icon class="mb-2" size="32">mdi-progress-clock</v-icon>
            <div class="text-h4">{{ activeProjects }}</div>
            <div class="text-body-2">Active Projects</div>
          </v-card-text>
        </v-card>
      </v-col>
      <v-col cols="12" lg="3" md="6">
        <v-card color="info" variant="tonal">
          <v-card-text class="text-center">
            <v-icon class="mb-2" size="32">mdi-format-list-checks</v-icon>
            <div class="text-h4">{{ totalTasks }}</div>
            <div class="text-body-2">Total Tasks</div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Projects Display -->
    <v-row v-if="projectsStore.projects.length > 0">
      <v-col>
        <!-- Card View -->
        <div v-if="viewMode === 'cards'">
          <v-row>
            <v-col
              v-for="project in projectsStore.projects"
              :key="project.id"
              cols="12"
              lg="4"
              md="6"
            >
              <ProjectCard
                :project="project"
                :selected="selectedProject?.id === project.id"
                show-actions
                @delete="deleteProject"
                @edit="editProject"
                @select="selectProject"
              />
            </v-col>
          </v-row>
        </div>

        <!-- List View -->
        <div v-else>
          <ProjectManager />
        </div>
      </v-col>
    </v-row>

    <!-- Empty State -->
    <v-row v-else>
      <v-col>
        <v-card class="text-center py-12" elevation="0" variant="outlined">
          <v-icon color="grey" size="80">mdi-folder-plus-outline</v-icon>
          <h2 class="text-h5 mt-4 mb-2">No Projects Yet</h2>
          <p class="text-body-1 text-medium-emphasis mb-6">
            Create your first project to start organizing your tasks and tracking progress
          </p>
          <v-btn
            color="primary"
            prepend-icon="mdi-plus"
            size="large"
            variant="elevated"
            @click="showCreateDialog = true"
          >
            Create Your First Project
          </v-btn>
        </v-card>
      </v-col>
    </v-row>

    <!-- Create Project Dialog -->
    <v-dialog v-model="showCreateDialog" max-width="600">
      <v-card>
        <v-card-title>
          {{ editingProject ? 'Edit Project' : 'Create New Project' }}
        </v-card-title>
        
        <v-card-text>
          <v-form ref="projectForm" @submit.prevent="saveProject">
            <v-text-field
              v-model="projectFormData.name"
              :rules="nameRules"
              density="compact"
              hide-details="auto"
              label="Project Name"
              prepend-inner-icon="mdi-folder"
              required
              variant="outlined"
            />
            
            <v-textarea
              v-model="projectFormData.description"
              class="mt-4"
              density="compact"
              hide-details="auto"
              label="Description (Optional)"
              prepend-inner-icon="mdi-text"
              rows="4"
              variant="outlined"
            />
          </v-form>
        </v-card-text>
        
        <v-card-actions>
          <v-spacer />
          <v-btn @click="cancelEdit">Cancel</v-btn>
          <v-btn
            :loading="saving"
            color="primary"
            variant="elevated"
            @click="saveProject"
          >
            {{ editingProject ? 'Update' : 'Create' }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Delete Confirmation Dialog -->
    <v-dialog v-model="showDeleteDialog" max-width="400">
      <v-card>
        <v-card-title class="text-h6">Delete Project</v-card-title>
        <v-card-text>
          Are you sure you want to delete "{{ projectToDelete?.name }}"?
          <br><br>
          <v-alert
            v-if="getProjectTaskCount(projectToDelete?.id) > 0"
            color="warning"
            variant="tonal"
          >
            This project has {{ getProjectTaskCount(projectToDelete?.id) }} tasks.
            You cannot delete a project with existing tasks.
          </v-alert>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn @click="showDeleteDialog = false">Cancel</v-btn>
          <v-btn
            :disabled="getProjectTaskCount(projectToDelete?.id) > 0"
            :loading="deleting"
            color="error"
            variant="elevated"
            @click="confirmDelete"
          >
            Delete
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue'
import { useProjectsStore } from '@/stores/projects'
import { useTasksStore } from '@/stores/tasks'
import ProjectCard from '@/components/projects/ProjectCard.vue'
import ProjectManager from '@/components/projects/ProjectManager.vue'

// Stores
const projectsStore = useProjectsStore()
const tasksStore = useTasksStore()

// Local state
const viewMode = ref('cards')
const selectedProject = ref(null)
const showCreateDialog = ref(false)
const showDeleteDialog = ref(false)
const saving = ref(false)
const deleting = ref(false)
const editingProject = ref(null)
const projectToDelete = ref(null)
const projectForm = ref(null)

const projectFormData = ref({
  name: '',
  description: '',
})

// Computed
const completedProjects = computed(() => {
  return projectsStore.projects.filter(project => {
    const stats = tasksStore.getProjectStatistics(project.id)
    return stats.totalTasks > 0 && stats.completionRate === 100
  }).length
})

const activeProjects = computed(() => {
  return projectsStore.projects.filter(project => {
    const stats = tasksStore.getProjectStatistics(project.id)
    return stats.totalTasks > 0 && stats.completionRate < 100
  }).length
})

const totalTasks = computed(() => {
  return tasksStore.tasks.length
})

// Validation rules
const nameRules = [
  v => !!v || 'Project name is required',
  v => (v && v.length >= 2) || 'Project name must be at least 2 characters',
  v => (v && v.length <= 100) || 'Project name must be less than 100 characters',
]

// Methods
const selectProject = (project) => {
  selectedProject.value = project
  projectsStore.selectProject(project)
}

const editProject = (project) => {
  editingProject.value = project
  projectFormData.value = {
    name: project.name,
    description: project.description || '',
  }
  showCreateDialog.value = true
}

const deleteProject = (project) => {
  projectToDelete.value = project
  showDeleteDialog.value = true
}

const getProjectTaskCount = (projectId) => {
  if (!projectId) return 0
  return tasksStore.filterTasksByProject(projectId).length
}

const saveProject = async () => {
  if (!projectForm.value) return
  
  const { valid } = await projectForm.value.validate()
  if (!valid) return

  saving.value = true
  try {
    if (editingProject.value) {
      await projectsStore.updateProject(editingProject.value.id, projectFormData.value)
    } else {
      const newProject = await projectsStore.addProject(projectFormData.value)
      if (newProject) {
        selectedProject.value = newProject
      }
    }
    
    cancelEdit()
  } catch (error) {
    console.error('Failed to save project:', error)
  } finally {
    saving.value = false
  }
}

const confirmDelete = async () => {
  if (!projectToDelete.value) return

  deleting.value = true
  try {
    await projectsStore.deleteProject(projectToDelete.value.id)
    showDeleteDialog.value = false
    projectToDelete.value = null
    
    // Clear selection if deleted project was selected
    if (selectedProject.value?.id === projectToDelete.value?.id) {
      selectedProject.value = null
    }
  } catch (error) {
    console.error('Failed to delete project:', error)
  } finally {
    deleting.value = false
  }
}

const cancelEdit = () => {
  showCreateDialog.value = false
  editingProject.value = null
  projectFormData.value = { name: '', description: '' }
  projectForm.value?.reset()
}

// Lifecycle
onMounted(async () => {
  await Promise.all([
    projectsStore.fetchProjects(),
    tasksStore.fetchTasks(),
  ])
  
  // Set initial selection
  if (projectsStore.selectedProject) {
    selectedProject.value = projectsStore.selectedProject
  }
})
</script>

<style scoped>
.v-card {
  transition: all 0.3s ease;
}
</style>
