/**
 * Database Service for PocketBase operations
 * Handles task storage and retrieval from a PocketBase backend
 */
import PocketBase from 'pocketbase'

class DatabaseService {
  constructor () {
    this.db = new PocketBase(import.meta.env.VITE_POCKETBASE_URL)
    this.collection = 'tasks'
    this.projectsCollection = 'projects'
  }

  async authenticate () {
    try {
      await this.db.admins.authWithPassword(import.meta.env.VITE_POCKETBASE_EMAIL, import.meta.env.VITE_POCKETBASE_PASSWORD)
      console.log('Authenticated successfully')
    } catch (error) {
      console.error('Failed to authenticate:', error)
      throw error
    }
  }

  async initDatabase () {
    // The PocketBase constructor initializes the connection, so this is a good place to check the connection
    try {
      await this.db.health.check()
      console.log('PocketBase connection established successfully')
    } catch (error) {
      console.error('Failed to connect to PocketBase:', error)
      throw error
    }
  }

  async ensureDatabase () {
    // With PocketBase, the connection is managed by the client, so we just ensure it's initialized
    if (!this.db) {
      this.initDatabase()
    }
  }

  /**
   * Insert tasks from JSON file into database
   * @param {Array} tasksData - Array of task objects from JSON
   * @param {string} projectId - The project ID to assign to all tasks
   * @returns {Object} - Result with success count and errors
   */
  async insertTasksFromJson (tasksData, projectId) {
    await this.ensureDatabase()

    if (!projectId) {
      throw new Error('Project ID is required for inserting tasks')
    }

    const results = {
      successful: 0,
      failed: 0,
      errors: [],
    }

    for (const task of tasksData) {
      try {
        if (!task.summary) {
          results.failed++
          results.errors.push(`Task missing required fields: ${task.id || 'Unknown ID'}`)
          continue
        }

        let parentId = null
        const idParts = task.id.split('-')
        if (idParts.length === 3) { // e.g., AAA-111-1
          parentId = `${idParts[0]}-${idParts[1]}`
        }

        const processedLinkedTasks = Array.isArray(task.linked_tasks)
          ? task.linked_tasks.map(linkedTaskId => {
              return {
                task_id: linkedTaskId,
                linkType: linkedTaskId === parentId ? 'Parent' : 'Requires',
              }
            })
          : []

        const taskToStore = {
          task_id: task.id, // Store the original ID from JSON
          parent_id: parentId, // Derived parent ID
          summary: task.summary,
          description: task.description || null,
          linked_tasks: processedLinkedTasks,
          epic: task.epic || null,
          priority: task.priority || 'Medium',
          estimated_effort: task.estimated_effort || 'Medium',
          type: task.type || 'Task',
          status: 'Backlog',
          assigned_to: task.assigned_to || null,
          project_id: projectId, // Required project reference
        }

        await this.db.collection(this.collection).create(taskToStore)
        results.successful++
      } catch (error) {
        results.failed++
        results.errors.push(`Failed to insert task ${task.id}: ${error.message}`)
      }
    }

    return results
  }

  /**
   * Get all tasks from database
   * @param {Object} filters - Optional filters for querying
   * @returns {Array} - Array of task objects
   */
  async getAllTasks (filters = {}) {
    await this.ensureDatabase()

    const filterOptions = []
    if (filters.epic) {
      filterOptions.push(`epic = '${filters.epic}'`)
    }
    if (filters.priority) {
      filterOptions.push(`priority = '${filters.priority}'`)
    }
    if (filters.type) {
      filterOptions.push(`type = '${filters.type}'`)
    }
    if (filters.status) {
      filterOptions.push(`status = '${filters.status}'`)
    }

    const filterString = filterOptions.join(' && ')

    const records = await this.db.collection(this.collection).getFullList({
      sort: '-created',
      filter: filterString,
      expand: 'project_id',
    })

    return records
  }

  /**
   * Get task by ID
   * @param {string} id - Task ID
   * @returns {Object|null} - Task object or null if not found
   */
  async getTaskById (id) {
    await this.ensureDatabase()
    try {
      const record = await this.db.collection(this.collection).getOne(id)
      return record
    } catch (error) {
      if (error.status === 404) {
        return null
      }
      throw error
    }
  }

  /**
   * Get task by its original task_id
   * @param {string} originalTaskId - The original task ID from the JSON file
   * @returns {Object|null} - Task object or null if not found
   */
  async getTaskByOriginalId (originalTaskId) {
    await this.ensureDatabase()
    try {
      const records = await this.db.collection(this.collection).getFullList({
        filter: `task_id = '${originalTaskId}'`,
      })
      return records.length > 0 ? records[0] : null
    } catch (error) {
      console.error(`Failed to fetch task by original ID ${originalTaskId}:`, error)
      throw error
    }
  }

  /**
   * Update task status
   * @param {string} id - Task ID
   * @param {string} status - New status
   * @returns {boolean} - Success status
   */
  async updateTaskStatus (id, status) {
    await this.ensureDatabase()
    try {
      // Normalize status to ensure consistency
      const normalizedStatus = status.trim()

      // Log the update for debugging
      console.log(`Updating task ${id} status to: "${normalizedStatus}"`)

      await this.db.collection(this.collection).update(id, { status: normalizedStatus })
      return true
    } catch (error) {
      console.error('Failed to update task status:', error)
      return false
    }
  }

  /**
   * Add a new task
   * @param {Object} taskData - The task data to add
   * @returns {Object} - The newly created task
   */
  async addTask (taskData) {
    await this.ensureDatabase()
    const record = await this.db.collection(this.collection).create(taskData)
    return record
  }

  /**
   * Update an existing task
   * @param {string} id - The ID of the task to update
   * @param {Object} taskData - The new task data
   * @returns {Object} - The updated task
   */
  async updateTask (id, taskData) {
    await this.ensureDatabase()
    const record = await this.db.collection(this.collection).update(id, taskData)
    return record
  }

  /**
   * Delete a task
   * @param {string} id - The ID of the task to delete
   * @returns {boolean} - Success status
   */
  async deleteTask (id) {
    await this.ensureDatabase()
    try {
      await this.db.collection(this.collection).delete(id)
      return true
    } catch (error) {
      console.error(`Failed to delete task ${id}:`, error)
      return false
    }
  }

  /**
   * Get database statistics
   * @returns {Object} - Statistics about the database
   */
  async getStatistics () {
    await this.ensureDatabase()
    const tasks = await this.getAllTasks()

    const stats = {
      totalTasks: tasks.length,
      tasksByType: {},
      tasksByPriority: {},
      tasksByStatus: {},
    }

    for (const task of tasks) {
      stats.tasksByType[task.type] = (stats.tasksByType[task.type] || 0) + 1
      stats.tasksByPriority[task.priority] = (stats.tasksByPriority[task.priority] || 0) + 1
      stats.tasksByStatus[task.status] = (stats.tasksByStatus[task.status] || 0) + 1
    }

    stats.tasksByType = Object.entries(stats.tasksByType).map(([type, count]) => ({ type, count }))
    stats.tasksByPriority = Object.entries(stats.tasksByPriority).map(([priority, count]) => ({ priority, count }))
    stats.tasksByStatus = Object.entries(stats.tasksByStatus).map(([status, count]) => ({ status, count }))

    return stats
  }

  /**
   * Clear all tasks from database
   * @returns {number} - Number of deleted tasks
   */
  async clearAllTasks () {
    await this.ensureDatabase()
    const tasks = await this.getAllTasks()
    const taskCount = tasks.length

    const deletePromises = tasks.map(task => this.db.collection(this.collection).delete(task.id))
    await Promise.all(deletePromises)

    return taskCount
  }

  // ===== PROJECT CRUD OPERATIONS =====

  /**
   * Get all projects from database
   * @param {Object} filters - Optional filters for querying
   * @returns {Array} - Array of project objects
   */
  async getAllProjects (filters = {}) {
    await this.ensureDatabase()

    const filterOptions = []
    if (filters.name) {
      filterOptions.push(`name ~ '${filters.name}'`)
    }

    const filterString = filterOptions.join(' && ')

    const records = await this.db.collection(this.projectsCollection).getFullList({
      sort: '-created',
      filter: filterString,
    })

    return records
  }

  /**
   * Get project by ID
   * @param {string} id - Project ID
   * @returns {Object|null} - Project object or null if not found
   */
  async getProjectById (id) {
    await this.ensureDatabase()
    try {
      const record = await this.db.collection(this.projectsCollection).getOne(id)
      return record
    } catch (error) {
      if (error.status === 404) {
        return null
      }
      throw error
    }
  }

  /**
   * Add a new project
   * @param {Object} projectData - The project data to add
   * @returns {Object} - The newly created project
   */
  async addProject (projectData) {
    await this.ensureDatabase()
    const record = await this.db.collection(this.projectsCollection).create(projectData)
    return record
  }

  /**
   * Update an existing project
   * @param {string} id - The ID of the project to update
   * @param {Object} projectData - The new project data
   * @returns {Object} - The updated project
   */
  async updateProject (id, projectData) {
    await this.ensureDatabase()
    const record = await this.db.collection(this.projectsCollection).update(id, projectData)
    return record
  }

  /**
   * Delete a project
   * @param {string} id - The ID of the project to delete
   * @returns {boolean} - Success status
   */
  async deleteProject (id) {
    await this.ensureDatabase()
    try {
      await this.db.collection(this.projectsCollection).delete(id)
      return true
    } catch (error) {
      console.error(`Failed to delete project ${id}:`, error)
      return false
    }
  }

  /**
   * Get tasks for a specific project
   * @param {string} projectId - The project ID
   * @param {Object} filters - Optional additional filters
   * @returns {Array} - Array of task objects
   */
  async getTasksByProject (projectId, filters = {}) {
    await this.ensureDatabase()

    const filterOptions = [`project_id = '${projectId}'`]

    if (filters.status) {
      filterOptions.push(`status = '${filters.status}'`)
    }
    if (filters.priority) {
      filterOptions.push(`priority = '${filters.priority}'`)
    }
    if (filters.type) {
      filterOptions.push(`type = '${filters.type}'`)
    }

    const filterString = filterOptions.join(' && ')

    const records = await this.db.collection(this.collection).getFullList({
      sort: '-created',
      filter: filterString,
      expand: 'project_id',
    })

    return records
  }

  /**
   * Clear all data from both tasks and projects collections
   * WARNING: This will delete all existing data
   */
  async clearAllData () {
    await this.ensureDatabase()

    try {
      // Clear tasks first (due to foreign key constraint)
      const taskCount = await this.clearAllTasks()

      // Clear projects
      const projects = await this.getAllProjects()
      const projectCount = projects.length

      const deletePromises = projects.map(project =>
        this.db.collection(this.projectsCollection).delete(project.id),
      )
      await Promise.all(deletePromises)

      console.log(`Cleared ${taskCount} tasks and ${projectCount} projects`)
      return { tasks: taskCount, projects: projectCount }
    } catch (error) {
      console.error('Failed to clear all data:', error)
      throw error
    }
  }

  /**
   * Close database connection
   */
  close () {
    // The PocketBase SDK doesn't require explicit connection closing.
    // This method is kept for API compatibility.
  }
}

// Export singleton instance
export default new DatabaseService()
