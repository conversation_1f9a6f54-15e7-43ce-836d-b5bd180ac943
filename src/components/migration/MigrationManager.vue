<template>
  <v-card elevation="2">
    <v-card-title class="d-flex align-center">
      <v-icon class="mr-2" color="warning">mdi-database-sync</v-icon>
      <span>Database Migration</span>
    </v-card-title>

    <v-card-text>
      <!-- Migration Status -->
      <v-alert
        v-if="migrationStatus"
        :color="migrationStatus.color"
        :icon="migrationStatus.icon"
        variant="tonal"
        class="mb-4"
      >
        {{ migrationStatus.message }}
      </v-alert>

      <!-- Migration Options -->
      <div v-if="!migrationCompleted" class="mb-4">
        <h3 class="text-h6 mb-3">Migration Options</h3>
        <v-checkbox
          v-model="migrateEpics"
          label="Create projects from existing task epics"
          hide-details
        />
        <div class="text-caption text-medium-emphasis mt-1">
          This will create separate projects for each unique epic value and assign tasks accordingly.
        </div>
      </div>

      <!-- Migration Progress -->
      <div v-if="migrationRunning" class="mb-4">
        <h3 class="text-h6 mb-3">Migration Progress</h3>
        <v-progress-linear
          :model-value="migrationProgress"
          color="primary"
          height="8"
          rounded
        />
        <div class="text-center mt-2">
          <span class="text-body-2">{{ currentStep }}</span>
        </div>
      </div>

      <!-- Migration Results -->
      <div v-if="migrationResult && migrationCompleted" class="mb-4">
        <h3 class="text-h6 mb-3">Migration Results</h3>

        <v-expansion-panels v-if="migrationResult.steps.length > 0">
          <v-expansion-panel
            v-for="(step, index) in migrationResult.steps"
            :key="index"
          >
            <v-expansion-panel-title>
              <div class="d-flex align-center">
                <v-icon
                  :color="step.success ? 'success' : 'error'"
                  class="mr-2"
                >
                  {{ step.success ? 'mdi-check-circle' : 'mdi-alert-circle' }}
                </v-icon>
                <span>{{ step.name }}</span>
                <v-spacer />
                <v-chip
                  :color="step.success ? 'success' : 'error'"
                  size="small"
                  variant="tonal"
                >
                  {{ step.success ? 'Success' : 'Failed' }}
                </v-chip>
              </div>
            </v-expansion-panel-title>

            <v-expansion-panel-text>
              <div v-if="step.tasksUpdated !== undefined" class="mb-2">
                <strong>Tasks Updated:</strong> {{ step.tasksUpdated }}
              </div>
              <div v-if="step.projectsCreated !== undefined" class="mb-2">
                <strong>Projects Created:</strong> {{ step.projectsCreated }}
              </div>
              <div v-if="step.totalTasks !== undefined" class="mb-2">
                <strong>Total Tasks:</strong> {{ step.totalTasks }}
              </div>
              <div v-if="step.tasksWithProject !== undefined" class="mb-2">
                <strong>Tasks with Projects:</strong> {{ step.tasksWithProject }}
              </div>
              <div v-if="step.totalProjects !== undefined" class="mb-2">
                <strong>Total Projects:</strong> {{ step.totalProjects }}
              </div>

              <div v-if="step.errors && step.errors.length > 0" class="mt-3">
                <strong class="text-error">Errors:</strong>
                <v-list density="compact">
                  <v-list-item
                    v-for="(error, errorIndex) in step.errors"
                    :key="errorIndex"
                    class="text-error"
                  >
                    <template #prepend>
                      <v-icon color="error" size="16">mdi-alert</v-icon>
                    </template>
                    <v-list-item-title class="text-body-2">
                      {{ error }}
                    </v-list-item-title>
                  </v-list-item>
                </v-list>
              </div>
            </v-expansion-panel-text>
          </v-expansion-panel>
        </v-expansion-panels>
      </div>

      <!-- Pre-migration Check Results -->
      <div v-if="checkResult && !migrationRunning" class="mb-4">
        <h3 class="text-h6 mb-3">Migration Check</h3>
        <v-alert
          :color="checkResult.needsMigration ? 'warning' : 'success'"
          :icon="checkResult.needsMigration ? 'mdi-alert' : 'mdi-check-circle'"
          variant="tonal"
        >
          {{ checkResult.needsMigration
            ? 'Migration is needed - some tasks are missing project assignments'
            : 'No migration needed - all tasks have project assignments' }}
        </v-alert>
      </div>
    </v-card-text>

    <v-card-actions>
      <v-btn
        v-if="!migrationCompleted && !migrationRunning"
        color="info"
        prepend-icon="mdi-magnify"
        variant="outlined"
        @click="checkMigrationStatus"
      >
        Check Status
      </v-btn>

      <v-btn
        v-if="!migrationCompleted && !migrationRunning && (checkResult?.needsMigration || !checkResult)"
        :disabled="migrationRunning"
        color="primary"
        prepend-icon="mdi-database-sync"
        variant="elevated"
        @click="runMigration"
      >
        Run Migration
      </v-btn>

      <v-btn
        v-if="migrationCompleted"
        color="success"
        prepend-icon="mdi-refresh"
        variant="outlined"
        @click="resetMigration"
      >
        Check Again
      </v-btn>

      <v-spacer />

      <v-btn
        v-if="migrationCompleted && migrationResult?.success"
        color="primary"
        prepend-icon="mdi-folder-multiple"
        variant="elevated"
        @click="$router.push('/projects')"
      >
        View Projects
      </v-btn>
    </v-card-actions>
  </v-card>
</template>

<script setup>
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import {
  isMigrationNeeded,
  runCompleteMigration,
  validateMigration
} from '@/utils/migration'

// Router
const router = useRouter()

// Reactive data
const migrationRunning = ref(false)
const migrationCompleted = ref(false)
const migrationProgress = ref(0)
const currentStep = ref('')
const migrateEpics = ref(false)
const migrationResult = ref(null)
const checkResult = ref(null)

// Computed
const migrationStatus = computed(() => {
  if (migrationRunning.value) {
    return {
      color: 'info',
      icon: 'mdi-loading',
      message: 'Migration in progress...',
    }
  }

  if (migrationCompleted.value && migrationResult.value) {
    if (migrationResult.value.success) {
      return {
        color: 'success',
        icon: 'mdi-check-circle',
        message: 'Migration completed successfully!',
      }
    } else {
      return {
        color: 'error',
        icon: 'mdi-alert-circle',
        message: `Migration completed with ${migrationResult.value.totalErrors} errors.`,
      }
    }
  }

  return null
})

// Methods
const checkMigrationStatus = async () => {
  try {
    const needsMigration = await isMigrationNeeded()
    checkResult.value = { needsMigration }
  } catch (error) {
    console.error('Failed to check migration status:', error)
    checkResult.value = { needsMigration: false, error: error.message }
  }
}

const runMigration = async () => {
  migrationRunning.value = true
  migrationProgress.value = 0
  migrationResult.value = null

  try {
    currentStep.value = 'Initializing migration...'
    migrationProgress.value = 10

    currentStep.value = 'Running migration process...'
    migrationProgress.value = 30

    const result = await runCompleteMigration({
      migrateEpics: migrateEpics.value
    })

    migrationProgress.value = 90
    currentStep.value = 'Finalizing...'

    migrationResult.value = result
    migrationProgress.value = 100
    migrationCompleted.value = true

  } catch (error) {
    console.error('Migration failed:', error)
    migrationResult.value = {
      success: false,
      steps: [{
        name: 'Migration Process',
        success: false,
        errors: [error.message],
      }],
      totalErrors: 1,
    }
    migrationCompleted.value = true
  } finally {
    migrationRunning.value = false
  }
}

const resetMigration = () => {
  migrationCompleted.value = false
  migrationResult.value = null
  checkResult.value = null
  migrationProgress.value = 0
  currentStep.value = ''
}
</script>

<style scoped>
.v-expansion-panel-title {
  font-weight: 500;
}
</style>
