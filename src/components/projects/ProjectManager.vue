<template>
  <v-card elevation="2">
    <v-card-title class="d-flex align-center justify-space-between">
      <div class="d-flex align-center">
        <v-icon class="mr-2" color="primary">mdi-folder-multiple</v-icon>
        <span>Project Management</span>
      </div>
      <v-btn
        color="primary"
        prepend-icon="mdi-plus"
        variant="elevated"
        @click="showCreateDialog = true"
      >
        New Project
      </v-btn>
    </v-card-title>

    <v-card-text>
      <!-- Projects List -->
      <v-list v-if="projectsStore.projects.length > 0">
        <v-list-item
          v-for="project in projectsStore.projects"
          :key="project.id"
          class="mb-2"
        >
          <template #prepend>
            <v-avatar color="primary" size="40">
              <v-icon color="white">mdi-folder</v-icon>
            </v-avatar>
          </template>

          <v-list-item-title class="font-weight-medium">
            {{ project.name }}
          </v-list-item-title>
          
          <v-list-item-subtitle>
            {{ project.description || 'No description' }}
          </v-list-item-subtitle>

          <template #append>
            <div class="d-flex align-center ga-2">
              <!-- Project Stats Chip -->
              <v-chip
                :color="getProjectTasksCount(project.id) > 0 ? 'primary' : 'grey'"
                size="small"
                variant="tonal"
              >
                {{ getProjectTasksCount(project.id) }} tasks
              </v-chip>
              
              <!-- Actions Menu -->
              <v-menu>
                <template #activator="{ props: menuProps }">
                  <v-btn
                    v-bind="menuProps"
                    icon="mdi-dots-vertical"
                    size="small"
                    variant="text"
                  />
                </template>
                <v-list>
                  <v-list-item @click="editProject(project)">
                    <template #prepend>
                      <v-icon>mdi-pencil</v-icon>
                    </template>
                    <v-list-item-title>Edit</v-list-item-title>
                  </v-list-item>
                  <v-list-item 
                    :disabled="getProjectTasksCount(project.id) > 0"
                    @click="deleteProject(project)"
                  >
                    <template #prepend>
                      <v-icon>mdi-delete</v-icon>
                    </template>
                    <v-list-item-title>Delete</v-list-item-title>
                  </v-list-item>
                </v-list>
              </v-menu>
            </div>
          </template>
        </v-list-item>
      </v-list>

      <!-- Empty State -->
      <div v-else class="text-center py-8">
        <v-icon color="grey" size="64">mdi-folder-plus</v-icon>
        <h3 class="text-h6 mt-4 mb-2">No Projects Yet</h3>
        <p class="text-body-2 text-medium-emphasis mb-4">
          Create your first project to start organizing your tasks
        </p>
        <v-btn
          color="primary"
          prepend-icon="mdi-plus"
          variant="elevated"
          @click="showCreateDialog = true"
        >
          Create Project
        </v-btn>
      </div>
    </v-card-text>

    <!-- Create/Edit Project Dialog -->
    <v-dialog v-model="showCreateDialog" max-width="600">
      <v-card>
        <v-card-title>
          {{ editingProject ? 'Edit Project' : 'Create New Project' }}
        </v-card-title>
        
        <v-card-text>
          <v-form ref="projectForm" @submit.prevent="saveProject">
            <v-text-field
              v-model="projectForm.name"
              :rules="nameRules"
              density="compact"
              hide-details="auto"
              label="Project Name"
              prepend-inner-icon="mdi-folder"
              required
              variant="outlined"
            />
            
            <v-textarea
              v-model="projectForm.description"
              class="mt-4"
              density="compact"
              hide-details="auto"
              label="Description (Optional)"
              prepend-inner-icon="mdi-text"
              rows="4"
              variant="outlined"
            />
          </v-form>
        </v-card-text>
        
        <v-card-actions>
          <v-spacer />
          <v-btn @click="cancelEdit">Cancel</v-btn>
          <v-btn
            :loading="saving"
            color="primary"
            variant="elevated"
            @click="saveProject"
          >
            {{ editingProject ? 'Update' : 'Create' }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Delete Confirmation Dialog -->
    <v-dialog v-model="showDeleteDialog" max-width="400">
      <v-card>
        <v-card-title class="text-h6">Delete Project</v-card-title>
        <v-card-text>
          Are you sure you want to delete the project "{{ projectToDelete?.name }}"?
          This action cannot be undone.
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn @click="showDeleteDialog = false">Cancel</v-btn>
          <v-btn
            :loading="deleting"
            color="error"
            variant="elevated"
            @click="confirmDelete"
          >
            Delete
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-card>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue'
import { useProjectsStore } from '@/stores/projects'
import { useTasksStore } from '@/stores/tasks'

// Stores
const projectsStore = useProjectsStore()
const tasksStore = useTasksStore()

// Local state
const showCreateDialog = ref(false)
const showDeleteDialog = ref(false)
const saving = ref(false)
const deleting = ref(false)
const editingProject = ref(null)
const projectToDelete = ref(null)
const projectForm = ref(null)

const projectFormData = ref({
  name: '',
  description: '',
})

// Computed
const getProjectTasksCount = computed(() => {
  return (projectId) => {
    return tasksStore.filterTasksByProject(projectId).length
  }
})

// Validation rules
const nameRules = [
  v => !!v || 'Project name is required',
  v => (v && v.length >= 2) || 'Project name must be at least 2 characters',
  v => (v && v.length <= 100) || 'Project name must be less than 100 characters',
]

// Methods
const editProject = (project) => {
  editingProject.value = project
  projectFormData.value = {
    name: project.name,
    description: project.description || '',
  }
  showCreateDialog.value = true
}

const deleteProject = (project) => {
  projectToDelete.value = project
  showDeleteDialog.value = true
}

const saveProject = async () => {
  if (!projectForm.value) return
  
  const { valid } = await projectForm.value.validate()
  if (!valid) return

  saving.value = true
  try {
    if (editingProject.value) {
      // Update existing project
      await projectsStore.updateProject(editingProject.value.id, projectFormData.value)
    } else {
      // Create new project
      await projectsStore.addProject(projectFormData.value)
    }
    
    cancelEdit()
  } catch (error) {
    console.error('Failed to save project:', error)
  } finally {
    saving.value = false
  }
}

const confirmDelete = async () => {
  if (!projectToDelete.value) return

  deleting.value = true
  try {
    await projectsStore.deleteProject(projectToDelete.value.id)
    showDeleteDialog.value = false
    projectToDelete.value = null
  } catch (error) {
    console.error('Failed to delete project:', error)
  } finally {
    deleting.value = false
  }
}

const cancelEdit = () => {
  showCreateDialog.value = false
  editingProject.value = null
  projectFormData.value = { name: '', description: '' }
  projectForm.value?.reset()
}

// Lifecycle
onMounted(async () => {
  // Fetch projects and tasks
  await Promise.all([
    projectsStore.fetchProjects(),
    tasksStore.fetchTasks(),
  ])
})
</script>

<style scoped>
.v-list-item {
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 8px;
}

.v-card {
  transition: all 0.3s ease;
}
</style>
