/**
 * Migration utilities for transitioning from old schema to new project-based schema
 */
import databaseService from '@/services/databaseService'
import databaseInitializer from '@/services/databaseInitializer'

/**
 * Check if migration is needed
 * @returns {Promise<boolean>} - True if migration is needed
 */
export async function isMigrationNeeded() {
  try {
    // Check if projects collection exists and has data
    const projects = await databaseService.getAllProjects()
    
    // Check if tasks exist without project_id
    const tasks = await databaseService.getAllTasks()
    const tasksWithoutProject = tasks.filter(task => !task.project_id)
    
    // Migration is needed if there are tasks without project assignment
    return tasksWithoutProject.length > 0
  } catch (error) {
    console.error('Error checking migration status:', error)
    return false
  }
}

/**
 * Perform migration from old schema to new project-based schema
 * @returns {Promise<Object>} - Migration result
 */
export async function performMigration() {
  const migrationResult = {
    success: false,
    tasksUpdated: 0,
    projectsCreated: 0,
    errors: [],
  }

  try {
    console.log('Starting migration to project-based schema...')

    // Step 1: Ensure database schema is up to date
    await databaseInitializer.initialize()

    // Step 2: Get all tasks
    const tasks = await databaseService.getAllTasks()
    const tasksWithoutProject = tasks.filter(task => !task.project_id)

    if (tasksWithoutProject.length === 0) {
      console.log('No migration needed - all tasks already have project assignments')
      migrationResult.success = true
      return migrationResult
    }

    console.log(`Found ${tasksWithoutProject.length} tasks that need project assignment`)

    // Step 3: Get or create default project
    const defaultProjectId = await databaseInitializer.getDefaultProjectId()
    
    if (!defaultProjectId) {
      throw new Error('Failed to get or create default project')
    }

    // Step 4: Update tasks to include project_id
    const updatePromises = tasksWithoutProject.map(async (task) => {
      try {
        await databaseService.updateTask(task.id, { project_id: defaultProjectId })
        migrationResult.tasksUpdated++
      } catch (error) {
        migrationResult.errors.push(`Failed to update task ${task.task_id}: ${error.message}`)
      }
    })

    await Promise.all(updatePromises)

    console.log(`Migration completed: ${migrationResult.tasksUpdated} tasks updated`)
    migrationResult.success = true

  } catch (error) {
    console.error('Migration failed:', error)
    migrationResult.errors.push(error.message)
  }

  return migrationResult
}

/**
 * Create projects from existing task epics
 * This is an optional migration step that creates projects based on existing epic values
 * @returns {Promise<Object>} - Migration result
 */
export async function migrateEpicsToProjects() {
  const migrationResult = {
    success: false,
    projectsCreated: 0,
    tasksUpdated: 0,
    errors: [],
  }

  try {
    console.log('Starting epic-to-project migration...')

    // Get all tasks
    const tasks = await databaseService.getAllTasks()
    
    // Group tasks by epic
    const epicGroups = {}
    tasks.forEach(task => {
      if (task.epic && task.epic.trim()) {
        const epic = task.epic.trim()
        if (!epicGroups[epic]) {
          epicGroups[epic] = []
        }
        epicGroups[epic].push(task)
      }
    })

    // Create projects for each epic
    for (const [epicName, epicTasks] of Object.entries(epicGroups)) {
      try {
        // Check if project already exists
        const existingProjects = await databaseService.getAllProjects({ name: epicName })
        let projectId

        if (existingProjects.length > 0) {
          projectId = existingProjects[0].id
          console.log(`Using existing project for epic "${epicName}"`)
        } else {
          // Create new project
          const newProject = await databaseService.addProject({
            name: epicName,
            description: `Project created from epic: ${epicName}`,
          })
          projectId = newProject.id
          migrationResult.projectsCreated++
          console.log(`Created project "${epicName}" from epic`)
        }

        // Update tasks to use the new project
        const updatePromises = epicTasks.map(async (task) => {
          try {
            await databaseService.updateTask(task.id, { project_id: projectId })
            migrationResult.tasksUpdated++
          } catch (error) {
            migrationResult.errors.push(`Failed to update task ${task.task_id}: ${error.message}`)
          }
        })

        await Promise.all(updatePromises)

      } catch (error) {
        migrationResult.errors.push(`Failed to process epic "${epicName}": ${error.message}`)
      }
    }

    console.log(`Epic migration completed: ${migrationResult.projectsCreated} projects created, ${migrationResult.tasksUpdated} tasks updated`)
    migrationResult.success = true

  } catch (error) {
    console.error('Epic migration failed:', error)
    migrationResult.errors.push(error.message)
  }

  return migrationResult
}

/**
 * Validate the migration results
 * @returns {Promise<Object>} - Validation result
 */
export async function validateMigration() {
  const validationResult = {
    success: false,
    totalTasks: 0,
    tasksWithProject: 0,
    tasksWithoutProject: 0,
    totalProjects: 0,
    errors: [],
  }

  try {
    // Get all tasks and projects
    const [tasks, projects] = await Promise.all([
      databaseService.getAllTasks(),
      databaseService.getAllProjects(),
    ])

    validationResult.totalTasks = tasks.length
    validationResult.totalProjects = projects.length

    // Check task-project relationships
    tasks.forEach(task => {
      if (task.project_id) {
        validationResult.tasksWithProject++
      } else {
        validationResult.tasksWithoutProject++
        validationResult.errors.push(`Task ${task.task_id} has no project assignment`)
      }
    })

    // Validate that all project references are valid
    const projectIds = new Set(projects.map(p => p.id))
    tasks.forEach(task => {
      if (task.project_id && !projectIds.has(task.project_id)) {
        validationResult.errors.push(`Task ${task.task_id} references non-existent project ${task.project_id}`)
      }
    })

    validationResult.success = validationResult.errors.length === 0

    console.log('Migration validation results:', {
      totalTasks: validationResult.totalTasks,
      tasksWithProject: validationResult.tasksWithProject,
      tasksWithoutProject: validationResult.tasksWithoutProject,
      totalProjects: validationResult.totalProjects,
      errors: validationResult.errors.length,
    })

  } catch (error) {
    console.error('Migration validation failed:', error)
    validationResult.errors.push(error.message)
  }

  return validationResult
}

/**
 * Run complete migration process
 * @param {Object} options - Migration options
 * @returns {Promise<Object>} - Complete migration result
 */
export async function runCompleteMigration(options = {}) {
  const { migrateEpics = false } = options
  
  const result = {
    success: false,
    steps: [],
    totalErrors: 0,
  }

  try {
    // Step 1: Check if migration is needed
    const needsMigration = await isMigrationNeeded()
    result.steps.push({
      name: 'Check Migration Status',
      success: true,
      needsMigration,
    })

    if (!needsMigration) {
      result.success = true
      return result
    }

    // Step 2: Perform basic migration
    const migrationResult = await performMigration()
    result.steps.push({
      name: 'Basic Migration',
      success: migrationResult.success,
      tasksUpdated: migrationResult.tasksUpdated,
      errors: migrationResult.errors,
    })
    result.totalErrors += migrationResult.errors.length

    // Step 3: Optionally migrate epics to projects
    if (migrateEpics) {
      const epicMigrationResult = await migrateEpicsToProjects()
      result.steps.push({
        name: 'Epic to Project Migration',
        success: epicMigrationResult.success,
        projectsCreated: epicMigrationResult.projectsCreated,
        tasksUpdated: epicMigrationResult.tasksUpdated,
        errors: epicMigrationResult.errors,
      })
      result.totalErrors += epicMigrationResult.errors.length
    }

    // Step 4: Validate migration
    const validationResult = await validateMigration()
    result.steps.push({
      name: 'Migration Validation',
      success: validationResult.success,
      totalTasks: validationResult.totalTasks,
      tasksWithProject: validationResult.tasksWithProject,
      totalProjects: validationResult.totalProjects,
      errors: validationResult.errors,
    })
    result.totalErrors += validationResult.errors.length

    result.success = result.totalErrors === 0

  } catch (error) {
    console.error('Complete migration failed:', error)
    result.steps.push({
      name: 'Migration Process',
      success: false,
      errors: [error.message],
    })
    result.totalErrors++
  }

  return result
}
