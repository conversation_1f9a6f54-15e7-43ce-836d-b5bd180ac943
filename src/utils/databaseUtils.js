/**
 * Database operation utilities
 * Provides common patterns for handling database operations with consistent error handling
 */

/**
 * Wraps database operations with consistent error handling
 * @param {Function} operation - The database operation to execute
 * @param {Object} options - Configuration options
 * @param {string} options.operationName - Name of the operation for error messages
 * @param {Object} options.errorRef - Vue ref for storing error messages
 * @param {Object} options.loadingRef - Vue ref for loading state (optional)
 * @param {Function} options.onSuccess - Callback for successful operations (optional)
 * @param {Function} options.onError - Custom error handler (optional)
 * @returns {Promise} - The result of the operation
 */
export const wrapDatabaseOperation = async (operation, options = {}) => {
  const {
    operationName = 'database operation',
    errorRef,
    loadingRef,
    onSuccess,
    onError,
  } = options

  if (loadingRef) {
    loadingRef.value = true
  }

  if (errorRef) {
    errorRef.value = null
  }

  try {
    const result = await operation()
    
    if (onSuccess) {
      await onSuccess(result)
    }
    
    return result
  } catch (error) {
    const errorMessage = `Failed to ${operationName}: ${error.message}`
    
    if (errorRef) {
      errorRef.value = errorMessage
    }
    
    console.error(`Error ${operationName}:`, error)
    
    if (onError) {
      await onError(error, errorMessage)
    } else {
      throw error
    }
  } finally {
    if (loadingRef) {
      loadingRef.value = false
    }
  }
}

/**
 * Specialized wrapper for CRUD operations on collections
 * @param {Object} params - Parameters for the operation
 * @param {Function} params.operation - The database operation
 * @param {string} params.entityName - Name of the entity (e.g., 'task', 'project')
 * @param {string} params.action - Action being performed (e.g., 'create', 'update', 'delete')
 * @param {Array} params.collection - Local collection to update
 * @param {Object} params.errorRef - Error reference
 * @param {Object} params.loadingRef - Loading reference
 * @param {Function} params.updateCollection - Function to update local collection
 * @returns {Promise} - The result of the operation
 */
export const wrapCrudOperation = async (params) => {
  const {
    operation,
    entityName,
    action,
    collection,
    errorRef,
    loadingRef,
    updateCollection,
  } = params

  return wrapDatabaseOperation(operation, {
    operationName: `${action} ${entityName}`,
    errorRef,
    loadingRef,
    onSuccess: async (result) => {
      if (updateCollection && collection) {
        updateCollection(result, collection)
      }
    },
  })
}

/**
 * Standard collection update functions for common CRUD operations
 */
export const collectionUpdaters = {
  /**
   * Add item to beginning of collection
   */
  create: (newItem, collection) => {
    collection.value.unshift(newItem)
  },

  /**
   * Update item in collection by ID
   */
  update: (updatedItem, collection, idField = 'id') => {
    const index = collection.value.findIndex(item => item[idField] === updatedItem[idField])
    if (index !== -1) {
      collection.value[index] = { ...collection.value[index], ...updatedItem }
    }
  },

  /**
   * Remove item from collection by ID
   */
  delete: (itemId, collection, idField = 'id') => {
    const index = collection.value.findIndex(item => item[idField] === itemId)
    if (index !== -1) {
      collection.value.splice(index, 1)
    }
  },

  /**
   * Replace entire collection
   */
  replace: (newItems, collection) => {
    collection.value = newItems
  },
}

/**
 * Creates a standard error handler that can be reused across stores
 */
export const createErrorHandler = (componentName) => {
  return (error, errorMessage) => {
    console.error(`[${componentName}] ${errorMessage}`, error)
    // Could extend to send to error reporting service
  }
}
