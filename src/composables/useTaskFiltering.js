// src/composables/useTaskFiltering.js

// This composable can be further enhanced, e.g., by making filters reactive if needed,
// but for now, it's a direct extraction of the logic from the tasks store.

export function useTaskFiltering() {
  const searchTasks = (tasksToSearch, query) => {
    if (!query || !query.trim()) {
      return tasksToSearch;
    }

    const searchTerm = query.toLowerCase();
    return tasksToSearch.filter(task =>
      task.summary.toLowerCase().includes(searchTerm) ||
      (task.description && task.description.toLowerCase().includes(searchTerm)) ||
      (task.epic && task.epic.toLowerCase().includes(searchTerm)) ||
      task.task_id.toLowerCase().includes(searchTerm)
    );
  };

  const filterTasks = (tasksToFilter, filters) => {
    let filteredTasks = [...tasksToFilter]; // Work on a copy

    if (filters.type && filters.type !== 'All') {
      filteredTasks = filteredTasks.filter(task => task.type === filters.type);
    }

    if (filters.priority && filters.priority !== 'All') {
      filteredTasks = filteredTasks.filter(task => task.priority === filters.priority);
    }

    if (filters.status && filters.status !== 'All') {
      // Case-insensitive comparison for status, ensure task.status exists
      filteredTasks = filteredTasks.filter(task =>
        task.status && task.status.trim().toLowerCase() === filters.status.trim().toLowerCase()
      );
    }

    if (filters.epic && filters.epic !== 'All') {
      filteredTasks = filteredTasks.filter(task => task.epic === filters.epic);
    }

    return filteredTasks;
  };

  return {
    searchTasks,
    filterTasks,
  };
}
