/**
 * Form management composable
 * Provides reusable form state management, validation, and submission handling
 */

import { ref, computed, watch, nextTick } from 'vue'

/**
 * Creates a form management system
 * @param {Object} initialData - Initial form data
 * @param {Object} options - Configuration options
 * @param {Object} options.validationRules - Validation rules for each field
 * @param {Function} options.onSubmit - Submit handler function
 * @param {Function} options.onReset - Reset handler function
 * @param {boolean} options.validateOnChange - Whether to validate on each change
 * @returns {Object} Form management interface
 */
export function useForm(initialData = {}, options = {}) {
  const {
    validationRules = {},
    onSubmit,
    onReset,
    validateOnChange = false,
  } = options

  // Form state
  const formData = ref({ ...initialData })
  const originalData = ref({ ...initialData })
  const errors = ref({})
  const touched = ref({})
  const isSubmitting = ref(false)
  const isValid = ref(true)
  const formRef = ref(null)

  /**
   * Computed properties
   */
  const isDirty = computed(() => {
    return JSON.stringify(formData.value) !== JSON.stringify(originalData.value)
  })

  const hasErrors = computed(() => {
    return Object.keys(errors.value).some(key => errors.value[key] && errors.value[key].length > 0)
  })

  const canSubmit = computed(() => {
    return isValid.value && isDirty.value && !isSubmitting.value
  })

  /**
   * Sets form data and optionally resets original data
   * @param {Object} data - New form data
   * @param {boolean} resetOriginal - Whether to update original data reference
   */
  const setFormData = (data, resetOriginal = true) => {
    formData.value = { ...data }
    if (resetOriginal) {
      originalData.value = { ...data }
      touched.value = {}
      errors.value = {}
    }
  }

  /**
   * Updates a specific field value
   * @param {string} field - Field name
   * @param {any} value - New value
   */
  const setField = (field, value) => {
    formData.value[field] = value
    touched.value[field] = true
    
    if (validateOnChange) {
      validateField(field)
    }
  }

  /**
   * Validates a specific field
   * @param {string} field - Field name
   * @returns {Promise<boolean>} Whether the field is valid
   */
  const validateField = async (field) => {
    const rules = validationRules[field]
    if (!rules || !Array.isArray(rules)) {
      errors.value[field] = []
      return true
    }

    const value = formData.value[field]
    const fieldErrors = []

    for (const rule of rules) {
      const result = typeof rule === 'function' ? await rule(value) : true
      if (result !== true) {
        fieldErrors.push(result)
        break // Stop at first error
      }
    }

    errors.value[field] = fieldErrors
    return fieldErrors.length === 0
  }

  /**
   * Validates all form fields
   * @returns {Promise<boolean>} Whether the entire form is valid
   */
  const validateForm = async () => {
    const validationPromises = Object.keys(validationRules).map(field => 
      validateField(field)
    )
    
    const results = await Promise.all(validationPromises)
    const formIsValid = results.every(result => result === true)
    
    isValid.value = formIsValid
    return formIsValid
  }

  /**
   * Validates form using Vuetify form ref if available
   * @returns {Promise<boolean>} Whether the form is valid
   */
  const validateVuetifyForm = async () => {
    if (formRef.value && typeof formRef.value.validate === 'function') {
      const { valid } = await formRef.value.validate()
      isValid.value = valid
      return valid
    }
    return await validateForm()
  }

  /**
   * Submits the form
   * @param {Event} event - Form submit event (optional)
   * @returns {Promise<any>} Result of submit handler
   */
  const submitForm = async (event) => {
    if (event) {
      event.preventDefault()
    }

    if (isSubmitting.value) {
      return
    }

    isSubmitting.value = true

    try {
      const isFormValid = await validateVuetifyForm()
      
      if (!isFormValid) {
        throw new Error('Form validation failed')
      }

      let result
      if (onSubmit) {
        result = await onSubmit(formData.value)
      }

      // Update original data after successful submit
      originalData.value = { ...formData.value }
      touched.value = {}

      return result
    } catch (error) {
      console.error('Form submission error:', error)
      throw error
    } finally {
      isSubmitting.value = false
    }
  }

  /**
   * Resets form to original state
   */
  const resetForm = () => {
    formData.value = { ...originalData.value }
    touched.value = {}
    errors.value = {}
    isValid.value = true

    if (formRef.value && typeof formRef.value.reset === 'function') {
      formRef.value.reset()
    }

    if (onReset) {
      onReset()
    }
  }

  /**
   * Resets form to initial state
   */
  const resetToInitial = () => {
    setFormData(initialData, true)
    
    if (formRef.value && typeof formRef.value.reset === 'function') {
      formRef.value.reset()
    }

    if (onReset) {
      onReset()
    }
  }

  /**
   * Marks all fields as touched (useful for showing all validation errors)
   */
  const touchAllFields = () => {
    Object.keys(formData.value).forEach(field => {
      touched.value[field] = true
    })
  }

  /**
   * Gets error message for a specific field
   * @param {string} field - Field name
   * @returns {string|null} Error message or null
   */
  const getFieldError = (field) => {
    const fieldErrors = errors.value[field]
    return fieldErrors && fieldErrors.length > 0 ? fieldErrors[0] : null
  }

  /**
   * Checks if a field has been touched
   * @param {string} field - Field name
   * @returns {boolean} Whether field has been touched
   */
  const isFieldTouched = (field) => {
    return touched.value[field] === true
  }

  /**
   * Checks if a field is valid
   * @param {string} field - Field name
   * @returns {boolean} Whether field is valid
   */
  const isFieldValid = (field) => {
    const fieldErrors = errors.value[field]
    return !fieldErrors || fieldErrors.length === 0
  }

  /**
   * Watch for changes if validateOnChange is enabled
   */
  if (validateOnChange) {
    watch(
      formData,
      async () => {
        await nextTick()
        await validateForm()
      },
      { deep: true }
    )
  }

  return {
    // State
    formData,
    originalData,
    errors,
    touched,
    isSubmitting,
    isValid,
    formRef,

    // Computed
    isDirty,
    hasErrors,
    canSubmit,

    // Methods
    setFormData,
    setField,
    validateField,
    validateForm,
    validateVuetifyForm,
    submitForm,
    resetForm,
    resetToInitial,
    touchAllFields,
    getFieldError,
    isFieldTouched,
    isFieldValid,
  }
}

/**
 * Specialized form hook for entity editing (create/update patterns)
 * @param {Object} initialData - Initial form data
 * @param {Object} options - Configuration options
 * @param {Function} options.createHandler - Function to handle creation
 * @param {Function} options.updateHandler - Function to handle updates
 * @param {Function} options.validationRules - Validation rules
 * @param {string} options.idField - Field name for entity ID (default: 'id')
 * @returns {Object} Entity form management interface
 */
export function useEntityForm(initialData = {}, options = {}) {
  const {
    createHandler,
    updateHandler,
    validationRules = {},
    idField = 'id',
  } = options

  const isEditMode = computed(() => {
    return formData.value[idField] != null
  })

  const submitHandler = async (data) => {
    if (isEditMode.value) {
      if (!updateHandler) {
        throw new Error('Update handler not provided')
      }
      return await updateHandler(data[idField], data)
    } else {
      if (!createHandler) {
        throw new Error('Create handler not provided')
      }
      return await createHandler(data)
    }
  }

  const form = useForm(initialData, {
    ...options,
    validationRules,
    onSubmit: submitHandler,
  })

  const { formData } = form

  return {
    ...form,
    isEditMode,
    isCreateMode: computed(() => !isEditMode.value),
  }
}

/**
 * Helper function to create field update handlers
 * @param {Function} setField - The setField function from useForm
 * @returns {Function} Field update handler
 */
export function createFieldUpdateHandler(setField) {
  return (field) => (value) => setField(field, value)
}

/**
 * Helper to create validation rules from field configurations
 * @param {Object} fieldConfigs - Field configuration object
 * @returns {Object} Validation rules object
 */
export function createValidationRules(fieldConfigs) {
  const rules = {}
  
  Object.entries(fieldConfigs).forEach(([field, config]) => {
    if (config.rules) {
      rules[field] = config.rules
    }
  })
  
  return rules
}
