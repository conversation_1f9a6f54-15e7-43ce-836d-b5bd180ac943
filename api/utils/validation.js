/**
 * Validation Utilities
 * Provides validation functions for API requests
 */

import { body, query, param, validationResult } from 'express-validator'
import { errors } from '../middleware/errorHandler.js'
import { CONFIG } from '../config/server.js'

/**
 * Handle validation errors
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {Function} next - Next middleware function
 */
export function handleValidationErrors(req, res, next) {
  const errors_validation = validationResult(req)
  
  if (!errors_validation.isEmpty()) {
    const errorDetails = errors_validation.array().map(error => ({
      field: error.path,
      message: error.msg,
      value: error.value
    }))
    
    return next(errors.VALIDATION_ERROR(errorDetails))
  }
  
  next()
}

/**
 * Common validation rules
 */
export const validators = {
  // Task validation rules
  taskId: param('task_id')
    .matches(/^[A-Z]{2,4}-[0-9]{1,4}(-[0-9]{1,2})?$/)
    .withMessage('Task ID must follow pattern: AAA-123 or AAA-123-1'),
  
  taskSummary: body('summary')
    .isString()
    .trim()
    .isLength({ min: 1, max: 500 })
    .withMessage('Summary must be between 1 and 500 characters'),
  
  taskDescription: body('description')
    .optional()
    .isString()
    .trim()
    .isLength({ max: 5000 })
    .withMessage('Description must be less than 5000 characters'),
  
  taskPriority: body('priority')
    .optional()
    .isIn(['Low', 'Medium', 'High'])
    .withMessage('Priority must be: Low, Medium, or High'),
  
  taskType: body('type')
    .optional()
    .isIn(['Task', 'Story', 'Epic', 'Bug'])
    .withMessage('Type must be: Task, Story, Epic, or Bug'),
  
  taskStatus: body('status')
    .optional()
    .isIn(['Backlog', 'In Progress', 'Done', 'Blocked'])
    .withMessage('Status must be: Backlog, In Progress, Done, or Blocked'),
  
  taskEstimatedEffort: body('estimated_effort')
    .optional()
    .isIn(['Small', 'Medium', 'Large'])
    .withMessage('Estimated effort must be: Small, Medium, or Large'),
  
  taskEpic: body('epic')
    .optional()
    .isString()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Epic must be less than 100 characters'),
  
  taskAssignedTo: body('assigned_to')
    .optional()
    .isString()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Assigned to must be less than 100 characters'),
  
  projectId: body('project_id')
    .isString()
    .trim()
    .notEmpty()
    .withMessage('Project ID is required'),
  
  // Project validation rules
  projectIdParam: param('project_id')
    .isString()
    .trim()
    .notEmpty()
    .withMessage('Project ID is required'),
  
  projectName: body('name')
    .isString()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Project name must be between 1 and 100 characters'),
  
  projectDescription: body('description')
    .optional()
    .isString()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Project description must be less than 1000 characters'),
  
  // Query parameter validation
  limitQuery: query('limit')
    .optional()
    .isInt({ min: 1, max: CONFIG.PAGINATION_MAX_LIMIT })
    .withMessage(`Limit must be between 1 and ${CONFIG.PAGINATION_MAX_LIMIT}`),
  
  offsetQuery: query('offset')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Offset must be a non-negative integer'),
  
  searchQuery: query('search')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('Search query must be between 1 and 200 characters'),
  
  statusQuery: query('status')
    .optional()
    .isIn(['Backlog', 'In Progress', 'Done', 'Blocked'])
    .withMessage('Status must be: Backlog, In Progress, Done, or Blocked'),
  
  priorityQuery: query('priority')
    .optional()
    .isIn(['Low', 'Medium', 'High'])
    .withMessage('Priority must be: Low, Medium, or High'),
  
  typeQuery: query('type')
    .optional()
    .isIn(['Task', 'Story', 'Epic', 'Bug'])
    .withMessage('Type must be: Task, Story, Epic, or Bug'),
  
  // Bulk operations validation
  bulkTaskIds: body('task_ids')
    .isArray({ min: 1, max: 100 })
    .withMessage('Task IDs must be an array with 1-100 items')
    .custom((value) => {
      for (const taskId of value) {
        if (!taskId.match(/^[A-Z]{2,4}-[0-9]{1,4}(-[0-9]{1,2})?$/)) {
          throw new Error(`Invalid task ID format: ${taskId}`)
        }
      }
      return true
    }),
  
  bulkUpdates: body('updates')
    .isArray({ min: 1, max: 100 })
    .withMessage('Updates must be an array with 1-100 items')
    .custom((value) => {
      for (const update of value) {
        if (!update.task_id || typeof update.task_id !== 'string') {
          throw new Error('Each update must have a valid task_id')
        }
        if (!update.updates || typeof update.updates !== 'object') {
          throw new Error('Each update must have an updates object')
        }
      }
      return true
    })
}

/**
 * Validation rule sets for different endpoints
 */
export const validationRules = {
  // Task endpoints
  createTask: [
    validators.taskSummary,
    validators.projectId,
    validators.taskDescription,
    validators.taskPriority,
    validators.taskType,
    validators.taskStatus,
    validators.taskEstimatedEffort,
    validators.taskEpic,
    validators.taskAssignedTo,
    handleValidationErrors
  ],
  
  updateTask: [
    validators.taskId,
    validators.taskSummary.optional(),
    validators.taskDescription,
    validators.taskPriority,
    validators.taskType,
    validators.taskStatus,
    validators.taskEstimatedEffort,
    validators.taskEpic,
    validators.taskAssignedTo,
    handleValidationErrors
  ],
  
  updateTaskStatus: [
    validators.taskId,
    body('status')
      .isIn(['Backlog', 'In Progress', 'Done', 'Blocked'])
      .withMessage('Status must be: Backlog, In Progress, Done, or Blocked'),
    handleValidationErrors
  ],
  
  getTask: [
    validators.taskId,
    handleValidationErrors
  ],
  
  getTasks: [
    validators.limitQuery,
    validators.offsetQuery,
    validators.searchQuery,
    validators.statusQuery,
    validators.priorityQuery,
    validators.typeQuery,
    query('project_id').optional().isString().trim(),
    query('epic').optional().isString().trim(),
    query('assigned_to').optional().isString().trim(),
    handleValidationErrors
  ],
  
  deleteTask: [
    validators.taskId,
    handleValidationErrors
  ],
  
  // Project endpoints
  createProject: [
    validators.projectName,
    validators.projectDescription,
    handleValidationErrors
  ],
  
  updateProject: [
    validators.projectIdParam,
    validators.projectName.optional(),
    validators.projectDescription,
    handleValidationErrors
  ],
  
  getProject: [
    validators.projectIdParam,
    handleValidationErrors
  ],
  
  deleteProject: [
    validators.projectIdParam,
    handleValidationErrors
  ],
  
  getProjectTasks: [
    validators.projectIdParam,
    validators.limitQuery,
    validators.offsetQuery,
    validators.statusQuery,
    validators.priorityQuery,
    validators.typeQuery,
    handleValidationErrors
  ],
  
  // Bulk operations
  bulkImportTasks: [
    validators.projectId,
    body('tasks')
      .isArray({ min: 1, max: 1000 })
      .withMessage('Tasks must be an array with 1-1000 items'),
    handleValidationErrors
  ],
  
  bulkUpdateTasks: [
    validators.bulkUpdates,
    handleValidationErrors
  ],
  
  bulkDeleteTasks: [
    validators.bulkTaskIds,
    handleValidationErrors
  ],
  
  // Search endpoints
  searchTasks: [
    query('q')
      .isString()
      .trim()
      .isLength({ min: 1, max: 200 })
      .withMessage('Search query is required and must be between 1 and 200 characters'),
    validators.limitQuery,
    validators.offsetQuery,
    validators.statusQuery,
    validators.priorityQuery,
    validators.typeQuery,
    query('project_id').optional().isString().trim(),
    handleValidationErrors
  ]
}

/**
 * Sanitize and normalize request data
 * @param {Object} data - Data to sanitize
 * @returns {Object} Sanitized data
 */
export function sanitizeData(data) {
  const sanitized = {}
  
  for (const [key, value] of Object.entries(data)) {
    if (value === null || value === undefined) {
      sanitized[key] = null
    } else if (typeof value === 'string') {
      sanitized[key] = value.trim()
    } else {
      sanitized[key] = value
    }
  }
  
  return sanitized
}

/**
 * Validate task ID format
 * @param {string} taskId - Task ID to validate
 * @returns {boolean} True if valid
 */
export function isValidTaskId(taskId) {
  return /^[A-Z]{2,4}-[0-9]{1,4}(-[0-9]{1,2})?$/.test(taskId)
}

/**
 * Validate UUID format
 * @param {string} uuid - UUID to validate
 * @returns {boolean} True if valid
 */
export function isValidUUID(uuid) {
  return /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(uuid)
}
