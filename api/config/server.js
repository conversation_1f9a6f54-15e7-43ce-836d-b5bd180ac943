/**
 * API Server Configuration
 * Sets up Express.js server with middleware and security configurations
 */

import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import compression from 'compression'
import morgan from 'morgan'
import rateLimit from 'express-rate-limit'
import { fileURLToPath } from 'url'
import { dirname } from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

/**
 * Create and configure Express application
 * @returns {Express} Configured Express app
 */
export function createApp() {
  const app = express()

  // Security middleware
  app.use(helmet({
    contentSecurityPolicy: false, // Disable CSP for API
    crossOriginEmbedderPolicy: false
  }))

  // CORS configuration
  app.use(cors({
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
  }))

  // Rate limiting
  const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: {
      error: {
        code: 'RATE_LIMIT_EXCEEDED',
        message: 'Too many requests from this IP, please try again later.',
        timestamp: new Date().toISOString()
      }
    },
    standardHeaders: true,
    legacyHeaders: false
  })

  app.use('/api/', limiter)

  // Compression
  app.use(compression())

  // Body parsing middleware
  app.use(express.json({ limit: '1mb' }))
  app.use(express.urlencoded({ extended: true, limit: '1mb' }))

  // Logging middleware
  if (process.env.NODE_ENV !== 'test') {
    app.use(morgan('combined'))
  }

  // Add request ID for debugging
  app.use((req, res, next) => {
    req.requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    res.setHeader('X-Request-ID', req.requestId)
    next()
  })

  return app
}

/**
 * Server configuration constants
 */
export const CONFIG = {
  PORT: process.env.API_PORT || 3001,
  NODE_ENV: process.env.NODE_ENV || 'development',
  API_PREFIX: '/api',
  VERSION: '1.0.0',
  
  // Request limits
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_JSON_SIZE: 1 * 1024 * 1024,  // 1MB
  
  // Database configuration
  POCKETBASE_URL: process.env.VITE_POCKETBASE_URL || 'http://localhost:8090',
  POCKETBASE_EMAIL: process.env.VITE_POCKETBASE_EMAIL,
  POCKETBASE_PASSWORD: process.env.VITE_POCKETBASE_PASSWORD,
  
  // Security
  API_KEY_HEADER: 'X-API-Key',
  JWT_SECRET: process.env.JWT_SECRET || 'your-secret-key-change-in-production',
  
  // Performance
  CACHE_TTL: 300, // 5 minutes
  PAGINATION_DEFAULT_LIMIT: 50,
  PAGINATION_MAX_LIMIT: 1000
}
