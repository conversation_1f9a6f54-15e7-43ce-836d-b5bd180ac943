/**
 * Project Service
 * Business logic layer for project operations
 * Integrates with existing databaseService.js
 */

import { CONFIG } from '../config/server.js'
import { errors } from '../middleware/errorHandler.js'
import { sanitizeData } from '../../common/validation/index.js'
import databaseService from '../../common/services/databaseService.js'
import { DEFAULTS } from '../../common/constants/index.js'

// Use shared database service directly
function getDatabaseService() {
  return databaseService
}

/**
 * Project Service Class
 * Handles all project-related business logic
 */
export class ProjectService {
  /**
   * Create a new project
   * @param {Object} projectData - Project data
   * @returns {Object} Created project
   */
  static async createProject(projectData) {
    try {
      const dbService = await getDatabaseService()
      const sanitizedData = sanitizeData(projectData)

      // Prepare project data
      const projectToCreate = {
        name: sanitizedData.name,
        description: sanitizedData.description || '',
      }

      // Create project using existing database service
      const createdProject = await dbService.addProject(projectToCreate)
      return createdProject
    } catch (error) {
      if (error.code) {
        throw error
      }
      throw errors.INTERNAL_SERVER_ERROR(`Failed to create project: ${error.message}`)
    }
  }

  /**
   * List all projects
   * @param {Object} filters - Filter parameters
   * @returns {Array} Projects array
   */
  static async listProjects(filters = {}) {
    try {
      const dbService = await getDatabaseService()

      // Build filter object for database service
      const dbFilters = {}
      if (filters.name) dbFilters.name = filters.name

      // Get projects from database
      const projects = await dbService.getAllProjects(dbFilters)
      return projects
    } catch (error) {
      if (error.code) {
        throw error
      }
      throw errors.INTERNAL_SERVER_ERROR(`Failed to list projects: ${error.message}`)
    }
  }

  /**
   * Get project by ID
   * @param {string} projectId - Project ID
   * @returns {Object|null} Project object or null
   */
  static async getProjectById(projectId) {
    try {
      const dbService = await getDatabaseService()
      const project = await dbService.getProjectById(projectId)
      return project
    } catch (error) {
      if (error.code) {
        throw error
      }
      throw errors.INTERNAL_SERVER_ERROR(`Failed to get project: ${error.message}`)
    }
  }

  /**
   * Update an existing project
   * @param {string} projectId - Project ID
   * @param {Object} updates - Updated project data
   * @returns {Object|null} Updated project or null
   */
  static async updateProject(projectId, updates) {
    try {
      const dbService = await getDatabaseService()
      const sanitizedUpdates = sanitizeData(updates)

      // Get existing project
      const existingProject = await dbService.getProjectById(projectId)
      if (!existingProject) {
        throw errors.NOT_FOUND('Project')
      }

      // Update project using existing database service
      const updatedProject = await dbService.updateProject(existingProject.id, sanitizedUpdates)
      return updatedProject
    } catch (error) {
      if (error.code) {
        throw error
      }
      throw errors.INTERNAL_SERVER_ERROR(`Failed to update project: ${error.message}`)
    }
  }

  /**
   * Delete a project
   * @param {string} projectId - Project ID
   * @returns {boolean} Success status
   */
  static async deleteProject(projectId) {
    try {
      const dbService = await getDatabaseService()

      // Get existing project
      const existingProject = await dbService.getProjectById(projectId)
      if (!existingProject) {
        throw errors.NOT_FOUND('Project')
      }

      // Ensure no associated tasks exist
      const projectTasks = await dbService.getTasksByProject(projectId)
      if (projectTasks.length > 0) {
        throw errors.CONFLICT('Cannot delete project with associated tasks')
      }

      // Delete project using existing database service
      const success = await dbService.deleteProject(existingProject.id)
      return success
    } catch (error) {
      if (error.code) {
        throw error
      }
      throw errors.INTERNAL_SERVER_ERROR(`Failed to delete project: ${error.message}`)
    }
  }

  /**
   * Get tasks for a specific project
   * @param {string} projectId - Project ID
   * @param {Object} filters - Filter parameters
   * @returns {Array} Tasks array
   */
  static async getProjectTasks(projectId, filters = {}) {
    try {
      const dbService = await getDatabaseService()

      // Get tasks for project using existing database service
      const tasks = await dbService.getTasksByProject(projectId, filters)
      return tasks
    } catch (error) {
      if (error.code) {
        throw error
      }
      throw errors.INTERNAL_SERVER_ERROR(`Failed to get project tasks: ${error.message}`)
    }
  }
}
